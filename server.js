import express from "express";
import http from "http";
import { createHmac, timingSafeEqual } from "crypto";
import { Server } from "socket.io";
import fetch from "node-fetch";
import Redis from "ioredis";

const SHARED_SECRET = process.env.SHARED_SECRET; // same used in Bubble
const BUBBLE_API_TOKEN = process.env.BUBBLE_API_TOKEN; // to patch notification status
const BUBBLE_APP_DOMAIN = process.env.BUBBLE_APP_URL; // adjust

const redis = new Redis(); // default config; adjust for production
const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: { origin: "*" },
});

app.use(express.json());

// socket presence
io.on("connection", (socket) => {
  const { user_id, auth_token } = socket.handshake.auth;
  // optionally verify auth_token if you issue session tokens from Bubble
  if (!user_id) {
    socket.disconnect();
    return;
  }
  // track presence: allow multiple sockets per user
  redis.sadd(`ws:user:${user_id}`, socket.id);
  socket.join(`user_${user_id}`);

  // fetch and emit missed notifications
  (async () => {
    try {
      const missed = await fetchMissedNotifications(user_id);
      for (const n of missed) {
        socket.emit("notification", {
          id: n._id,
          type: n.type,
          payload: n.payload,
          created_at: n.created_at,
        });
        // mark delivered in Bubble if not already
        await fetch(`https://${process.env.BUBBLE_APP_DOMAIN}/api/1.1/obj/Notification/${n._id}`, {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${process.env.BUBBLE_API_TOKEN}`,
          },
          body: JSON.stringify({
            status: "sent",
            delivered_at: new Date().toISOString(),
          }),
        });
      }
    } catch (e) {
      console.error("failed fetching missed notifications", e);
    }
  })();
  
  socket.on("disconnect", () => {
    redis.srem(`ws:user:${user_id}`, socket.id);
  });
});

// verify HMAC helper
function verifyHmac({ notification_id, recipient_id, timestamp, hmac }) {
  const payload = `${notification_id}|${recipient_id}|${timestamp}`;
  const expected = createHmac("sha256", SHARED_SECRET).update(payload).digest("hex");

  // constant-time compare
  const bufExpected = Buffer.from(expected, "utf8");
  const bufProvided = Buffer.from(hmac, "utf8");
  if (bufExpected.length !== bufProvided.length) return false;
  return timingSafeEqual(bufExpected, bufProvided);
}

// Endpoint Bubble calls when new notification is created
app.post("/notify", async (req, res) => {
  const { recipient_id, type, payload, bubble_notification_id, timestamp, hmac } = req.body;
  if (!verifyHmac({ notification_id: bubble_notification_id, recipient_id, timestamp, hmac })) {
    return res.status(401).json({ error: "invalid signature" });
  }

  // check presence
  const sockets = await redis.smembers(`ws:user:${recipient_id}`);
  if (sockets && sockets.length) {
    // emit to all connected sockets for that user
    io.to(`user_${recipient_id}`).emit("notification", {
      id: bubble_notification_id,
      type,
      payload,
      created_at: new Date().toISOString(),
    });

    // mark delivered in Bubble: PATCH the Notification object
    await fetch(`https://${BUBBLE_APP_DOMAIN}/api/1.1/obj/Notification/${bubble_notification_id}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BUBBLE_API_TOKEN}`,
      },
      body: JSON.stringify({
        status: "sent",
        delivered_at: new Date().toISOString(),
      }),
    });
  } else {
    // offline: leave it pending; Bubble UI on next login will pick it up
  }

  res.json({ ok: true });
});

const PORT = process.env.PORT || 4000;
server.listen(PORT, () => {
  console.log(`notify service running on ${PORT}`);
});

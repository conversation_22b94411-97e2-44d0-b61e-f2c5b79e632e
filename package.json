{"name": "realtime-notify-service", "version": "0.1.0", "description": "Node.js websocket-backed notification relay for Bubble.io", "type": "module", "engines": {"node": ">=18"}, "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "ioredis": "^5.3.2", "dotenv": "^16.3.1", "node-fetch": "^3.3.2"}, "devDependencies": {"nodemon": "^2.0.22"}, "keywords": ["websocket", "notifications", "bubble.io", "socket.io", "redis"], "author": "", "license": "MIT"}